import UIKit
import app_links
import Flutter
import flutter_local_notifications
import Firebase
import UserNotifications

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
      FlutterLocalNotificationsPlugin.setPluginRegistrantCallback { (registry) in
        GeneratedPluginRegistrant.register(with: registry)
      }

      if #available(iOS 10.0, *) {
        UNUserNotificationCenter.current().delegate = self

        // Solicita permissões de notificação explicitamente
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
          print("🔔 iOS: Notification permission granted: \(granted)")
          if let error = error {
            print("🔔 iOS: Notification permission error: \(error)")
          }
        }
      }

      // Configure Firebase
      FirebaseApp.configure()

      // Register for remote notifications
      application.registerForRemoteNotifications()


    GeneratedPluginRegistrant.register(with: self)
     // Retrieve the link from parameters
    if let url = AppLinks.shared.getLink(launchOptions: launchOptions) {
      // We have a link, propagate it to your Flutter app or not
      AppLinks.shared.handleLink(url: url)
      return true // Returning true will stop the propagation to other packages
    }
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // Handle background app refresh for silent push notifications
  override func application(
    _ application: UIApplication,
    didReceiveRemoteNotification userInfo: [AnyHashable: Any],
    fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void
  ) {
    print("🔔 iOS: Received remote notification in background")
    print("🔔 iOS: UserInfo: \(userInfo)")

    // Let Firebase handle the notification
    super.application(application, didReceiveRemoteNotification: userInfo, fetchCompletionHandler: completionHandler)
  }

  // Handle APNs token registration
  override func application(
    _ application: UIApplication,
    didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
  ) {
    print("🔔 iOS: Registered for remote notifications")
    super.application(application, didRegisterForRemoteNotificationsWithDeviceToken: deviceToken)
  }

  // Handle APNs registration failure
  override func application(
    _ application: UIApplication,
    didFailToRegisterForRemoteNotificationsWithError error: Error
  ) {
    print("🔔 iOS: Failed to register for remote notifications: \(error)")
    super.application(application, didFailToRegisterForRemoteNotificationsWithError: error)
  }

  // MARK: - UNUserNotificationCenterDelegate
  // Chamado quando uma notificação é recebida enquanto o app está em foreground
  override func userNotificationCenter(
    _ center: UNUserNotificationCenter,
    willPresent notification: UNNotification,
    withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
  ) {
    print("🔔 iOS: Will present notification in foreground")
    // Mostra a notificação mesmo quando o app está em foreground
    completionHandler([.alert, .sound, .badge])
  }

  // Chamado quando o usuário toca na notificação
  override func userNotificationCenter(
    _ center: UNUserNotificationCenter,
    didReceive response: UNNotificationResponse,
    withCompletionHandler completionHandler: @escaping () -> Void
  ) {
    print("🔔 iOS: Did receive notification response")
    completionHandler()
  }
}
